[move]
version = 3
manifest_digest = "34D11EAEE03A02995186B4F9F699980BA65978A1D65C3BE261BD79660C5AD4C5"
deps_digest = "8ADDF07E60EF9ABEFEBFE3115F6D8E269A96EAED8060B41FE1C8B9AB5C34A73C"

[[move.dependencies]]
id = "Bridge"
name = "Bridge"

[[move.dependencies]]
id = "EndpointPtbBuilder"
name = "EndpointPtbBuilder"

[[move.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.dependencies]]
id = "OApp"
name = "OApp"

[[move.dependencies]]
id = "Sui"
name = "Sui"

[[move.dependencies]]
id = "SuiSystem"
name = "SuiSystem"

[[move.dev-dependencies]]
id = "Dvn"
name = "DVN"

[[move.dev-dependencies]]
id = "DvnFeeLib"
name = "DVNFeeLib"

[[move.dev-dependencies]]
id = "Executor"
name = "Executor"

[[move.dev-dependencies]]
id = "ExecutorFeeLib"
name = "ExecutorFeeLib"

[[move.dev-dependencies]]
id = "PriceFeed"
name = "PriceFeed"

[[move.dev-dependencies]]
id = "SimpleMessageLib"
name = "SimpleMessageLib"

[[move.dev-dependencies]]
id = "Treasury"
name = "Treasury"

[[move.dev-dependencies]]
id = "Uln302"
name = "Uln302"

[[move.package]]
id = "Bridge"

[move.package.source]
git = "https://github.com/MystenLabs/sui.git"
rev = "db951a5ea6b125f6253a6b7f436ddba46eb0feb3"
subdir = "crates/sui-framework/packages/bridge"

[[move.package.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.package.dependencies]]
id = "Sui"
name = "Sui"

[[move.package.dependencies]]
id = "SuiSystem"
name = "SuiSystem"

[[move.package]]
id = "Call"

[move.package.source]
local = "../../call"

[[move.package.dependencies]]
id = "Bridge"
name = "Bridge"

[[move.package.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.package.dependencies]]
id = "Sui"
name = "Sui"

[[move.package.dependencies]]
id = "SuiSystem"
name = "SuiSystem"

[[move.package.dependencies]]
id = "Utils"
name = "Utils"

[[move.package]]
id = "Dvn"

[move.package.source]
local = "../../workers/dvns/dvn"

[[move.package.dependencies]]
id = "Bridge"
name = "Bridge"

[[move.package.dependencies]]
id = "DvnCallType"
name = "DvnCallType"

[[move.package.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.package.dependencies]]
id = "MsglibPtbBuilderCallTypes"
name = "MsglibPtbBuilderCallTypes"

[[move.package.dependencies]]
id = "Sui"
name = "Sui"

[[move.package.dependencies]]
id = "SuiSystem"
name = "SuiSystem"

[[move.package.dependencies]]
id = "Uln302"
name = "Uln302"

[[move.package.dependencies]]
id = "WorkerCommon"
name = "WorkerCommon"

[[move.package]]
id = "DvnCallType"

[move.package.source]
local = "../../workers/dvns/dvn-call-type"

[[move.package.dependencies]]
id = "Bridge"
name = "Bridge"

[[move.package.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.package.dependencies]]
id = "Sui"
name = "Sui"

[[move.package.dependencies]]
id = "SuiSystem"
name = "SuiSystem"

[[move.package]]
id = "DvnFeeLib"

[move.package.source]
local = "../../workers/dvns/dvn-fee-lib"

[[move.package.dependencies]]
id = "Bridge"
name = "Bridge"

[[move.package.dependencies]]
id = "Call"
name = "Call"

[[move.package.dependencies]]
id = "DvnCallType"
name = "DvnCallType"

[[move.package.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.package.dependencies]]
id = "PriceFeedCallTypes"
name = "PriceFeedCallTypes"

[[move.package.dependencies]]
id = "Sui"
name = "Sui"

[[move.package.dependencies]]
id = "SuiSystem"
name = "SuiSystem"

[[move.package.dependencies]]
id = "WorkerCommon"
name = "WorkerCommon"

[[move.package]]
id = "EndpointPtbBuilder"

[move.package.source]
local = "../../ptb-builders/endpoint-ptb-builder"

[[move.package.dependencies]]
id = "Bridge"
name = "Bridge"

[[move.package.dependencies]]
id = "Call"
name = "Call"

[[move.package.dependencies]]
id = "EndpointV2"
name = "EndpointV2"

[[move.package.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.package.dependencies]]
id = "PtbMoveCall"
name = "PtbMoveCall"

[[move.package.dependencies]]
id = "Sui"
name = "Sui"

[[move.package.dependencies]]
id = "SuiSystem"
name = "SuiSystem"

[[move.package]]
id = "EndpointV2"

[move.package.source]
local = "../../endpoint-v2"

[[move.package.dependencies]]
id = "Bridge"
name = "Bridge"

[[move.package.dependencies]]
id = "Call"
name = "Call"

[[move.package.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.package.dependencies]]
id = "Sui"
name = "Sui"

[[move.package.dependencies]]
id = "SuiSystem"
name = "SuiSystem"

[[move.package.dependencies]]
id = "Utils"
name = "Utils"

[[move.package.dependencies]]
id = "Zro"
name = "Zro"

[[move.package]]
id = "Executor"

[move.package.source]
local = "../../workers/executors/executor"

[[move.package.dependencies]]
id = "Bridge"
name = "Bridge"

[[move.package.dependencies]]
id = "ExecutorCallType"
name = "ExecutorCallType"

[[move.package.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.package.dependencies]]
id = "MsglibPtbBuilderCallTypes"
name = "MsglibPtbBuilderCallTypes"

[[move.package.dependencies]]
id = "Sui"
name = "Sui"

[[move.package.dependencies]]
id = "SuiSystem"
name = "SuiSystem"

[[move.package.dependencies]]
id = "Uln302"
name = "Uln302"

[[move.package.dependencies]]
id = "WorkerCommon"
name = "WorkerCommon"

[[move.package]]
id = "ExecutorCallType"

[move.package.source]
local = "../../workers/executors/executor-call-type"

[[move.package.dependencies]]
id = "Bridge"
name = "Bridge"

[[move.package.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.package.dependencies]]
id = "Sui"
name = "Sui"

[[move.package.dependencies]]
id = "SuiSystem"
name = "SuiSystem"

[[move.package]]
id = "ExecutorFeeLib"

[move.package.source]
local = "../../workers/executors/executor-fee-lib"

[[move.package.dependencies]]
id = "Bridge"
name = "Bridge"

[[move.package.dependencies]]
id = "Call"
name = "Call"

[[move.package.dependencies]]
id = "ExecutorCallType"
name = "ExecutorCallType"

[[move.package.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.package.dependencies]]
id = "PriceFeedCallTypes"
name = "PriceFeedCallTypes"

[[move.package.dependencies]]
id = "Sui"
name = "Sui"

[[move.package.dependencies]]
id = "SuiSystem"
name = "SuiSystem"

[[move.package.dependencies]]
id = "WorkerCommon"
name = "WorkerCommon"

[[move.package]]
id = "MessageLibCommon"

[move.package.source]
local = "../../message-libs/message-lib-common"

[[move.package.dependencies]]
id = "Bridge"
name = "Bridge"

[[move.package.dependencies]]
id = "EndpointV2"
name = "EndpointV2"

[[move.package.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.package.dependencies]]
id = "Sui"
name = "Sui"

[[move.package.dependencies]]
id = "SuiSystem"
name = "SuiSystem"

[[move.package]]
id = "MoveStdlib"

[move.package.source]
git = "https://github.com/MystenLabs/sui.git"
rev = "db951a5ea6b125f6253a6b7f436ddba46eb0feb3"
subdir = "crates/sui-framework/packages/move-stdlib"

[[move.package]]
id = "MsglibPtbBuilderCallTypes"

[move.package.source]
local = "../../ptb-builders/msglib-ptb-builders/msglib-ptb-builder-call-types"

[[move.package.dependencies]]
id = "Bridge"
name = "Bridge"

[[move.package.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.package.dependencies]]
id = "PtbMoveCall"
name = "PtbMoveCall"

[[move.package.dependencies]]
id = "Sui"
name = "Sui"

[[move.package.dependencies]]
id = "SuiSystem"
name = "SuiSystem"

[[move.package]]
id = "OApp"

[move.package.source]
local = "../oapp"

[[move.package.dependencies]]
id = "Bridge"
name = "Bridge"

[[move.package.dependencies]]
id = "Call"
name = "Call"

[[move.package.dependencies]]
id = "EndpointV2"
name = "EndpointV2"

[[move.package.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.package.dependencies]]
id = "Sui"
name = "Sui"

[[move.package.dependencies]]
id = "SuiSystem"
name = "SuiSystem"

[[move.package]]
id = "PriceFeed"

[move.package.source]
local = "../../workers/price-feed"

[[move.package.dependencies]]
id = "Bridge"
name = "Bridge"

[[move.package.dependencies]]
id = "Call"
name = "Call"

[[move.package.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.package.dependencies]]
id = "PriceFeedCallTypes"
name = "PriceFeedCallTypes"

[[move.package.dependencies]]
id = "Sui"
name = "Sui"

[[move.package.dependencies]]
id = "SuiSystem"
name = "SuiSystem"

[[move.package.dependencies]]
id = "Utils"
name = "Utils"

[[move.package]]
id = "PriceFeedCallTypes"

[move.package.source]
local = "../../workers/price-feed-call-types"

[[move.package.dependencies]]
id = "Bridge"
name = "Bridge"

[[move.package.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.package.dependencies]]
id = "Sui"
name = "Sui"

[[move.package.dependencies]]
id = "SuiSystem"
name = "SuiSystem"

[[move.package]]
id = "PtbMoveCall"

[move.package.source]
local = "../../ptb-builders/ptb-move-call"

[[move.package.dependencies]]
id = "Bridge"
name = "Bridge"

[[move.package.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.package.dependencies]]
id = "Sui"
name = "Sui"

[[move.package.dependencies]]
id = "SuiSystem"
name = "SuiSystem"

[[move.package.dependencies]]
id = "Utils"
name = "Utils"

[[move.package]]
id = "SimpleMessageLib"

[move.package.source]
local = "../../message-libs/simple-message-lib"

[[move.package.dependencies]]
id = "Bridge"
name = "Bridge"

[[move.package.dependencies]]
id = "MessageLibCommon"
name = "MessageLibCommon"

[[move.package.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.package.dependencies]]
id = "Sui"
name = "Sui"

[[move.package.dependencies]]
id = "SuiSystem"
name = "SuiSystem"

[[move.package.dependencies]]
id = "Utils"
name = "Utils"

[[move.package]]
id = "Sui"

[move.package.source]
git = "https://github.com/MystenLabs/sui.git"
rev = "db951a5ea6b125f6253a6b7f436ddba46eb0feb3"
subdir = "crates/sui-framework/packages/sui-framework"

[[move.package.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.package]]
id = "SuiSystem"

[move.package.source]
git = "https://github.com/MystenLabs/sui.git"
rev = "db951a5ea6b125f6253a6b7f436ddba46eb0feb3"
subdir = "crates/sui-framework/packages/sui-system"

[[move.package.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.package.dependencies]]
id = "Sui"
name = "Sui"

[[move.package]]
id = "Treasury"

[move.package.source]
local = "../../message-libs/treasury"

[[move.package.dependencies]]
id = "Bridge"
name = "Bridge"

[[move.package.dependencies]]
id = "MessageLibCommon"
name = "MessageLibCommon"

[[move.package.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.package.dependencies]]
id = "Sui"
name = "Sui"

[[move.package.dependencies]]
id = "SuiSystem"
name = "SuiSystem"

[[move.package.dependencies]]
id = "Utils"
name = "Utils"

[[move.package]]
id = "Uln302"

[move.package.source]
local = "../../message-libs/uln-302"

[[move.package.dependencies]]
id = "Bridge"
name = "Bridge"

[[move.package.dependencies]]
id = "MessageLibCommon"
name = "MessageLibCommon"

[[move.package.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.package.dependencies]]
id = "Sui"
name = "Sui"

[[move.package.dependencies]]
id = "SuiSystem"
name = "SuiSystem"

[[move.package.dependencies]]
id = "Treasury"
name = "Treasury"

[[move.package.dependencies]]
id = "Utils"
name = "Utils"

[[move.package]]
id = "Utils"

[move.package.source]
local = "../../utils"

[[move.package.dependencies]]
id = "Bridge"
name = "Bridge"

[[move.package.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.package.dependencies]]
id = "Sui"
name = "Sui"

[[move.package.dependencies]]
id = "SuiSystem"
name = "SuiSystem"

[[move.package]]
id = "WorkerCommon"

[move.package.source]
local = "../../workers/worker-common"

[[move.package.dependencies]]
id = "Bridge"
name = "Bridge"

[[move.package.dependencies]]
id = "Call"
name = "Call"

[[move.package.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.package.dependencies]]
id = "Sui"
name = "Sui"

[[move.package.dependencies]]
id = "SuiSystem"
name = "SuiSystem"

[[move.package.dependencies]]
id = "Utils"
name = "Utils"

[[move.package]]
id = "Zro"

[move.package.source]
local = "../../zro"

[[move.package.dependencies]]
id = "Bridge"
name = "Bridge"

[[move.package.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.package.dependencies]]
id = "Sui"
name = "Sui"

[[move.package.dependencies]]
id = "SuiSystem"
name = "SuiSystem"

[move.toolchain-version]
compiler-version = "1.54.1"
edition = "2024.beta"
flavor = "sui"

[env]
[env.testnet]
chain-id = "4c78adac"
original-published-id = "0xa6a19e84c7df2014e384cdd56f5674e5a0c06b92cc5d33fca89633680a9ebcd7"
latest-published-id = "0xa6a19e84c7df2014e384cdd56f5674e5a0c06b92cc5d33fca89633680a9ebcd7"
published-version = "1"

[env.mainnet]
chain-id = "35834a8a"
original-published-id = "0xfe1e336fdb42fa5ef85ab9a9c932b58c43e0bb58273cecea9d00cb5d05159914"
latest-published-id = "0xfe1e336fdb42fa5ef85ab9a9c932b58c43e0bb58273cecea9d00cb5d05159914"
published-version = "1"
