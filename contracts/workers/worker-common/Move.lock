[move]
version = 3
manifest_digest = "18BA3A156540C9B5E8E6B9F67528E67E399405F059BAE8C4CD5CEFF5C83E99EB"
deps_digest = "52B406A7A21811BEF51751CF88DA0E76DAEFFEAC888D4F4060B1A72BBE7D8D35"

[[move.dependencies]]
id = "Bridge"
name = "Bridge"

[[move.dependencies]]
id = "Call"
name = "Call"

[[move.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.dependencies]]
id = "Sui"
name = "Sui"

[[move.dependencies]]
id = "SuiSystem"
name = "SuiSystem"

[[move.dependencies]]
id = "Utils"
name = "Utils"

[[move.package]]
id = "Bridge"

[move.package.source]
git = "https://github.com/MystenLabs/sui.git"
rev = "db951a5ea6b125f6253a6b7f436ddba46eb0feb3"
subdir = "crates/sui-framework/packages/bridge"

[[move.package.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.package.dependencies]]
id = "Sui"
name = "Sui"

[[move.package.dependencies]]
id = "SuiSystem"
name = "SuiSystem"

[[move.package]]
id = "Call"

[move.package.source]
local = "../../call"

[[move.package.dependencies]]
id = "Bridge"
name = "Bridge"

[[move.package.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.package.dependencies]]
id = "Sui"
name = "Sui"

[[move.package.dependencies]]
id = "SuiSystem"
name = "SuiSystem"

[[move.package.dependencies]]
id = "Utils"
name = "Utils"

[[move.package]]
id = "MoveStdlib"

[move.package.source]
git = "https://github.com/MystenLabs/sui.git"
rev = "db951a5ea6b125f6253a6b7f436ddba46eb0feb3"
subdir = "crates/sui-framework/packages/move-stdlib"

[[move.package]]
id = "Sui"

[move.package.source]
git = "https://github.com/MystenLabs/sui.git"
rev = "db951a5ea6b125f6253a6b7f436ddba46eb0feb3"
subdir = "crates/sui-framework/packages/sui-framework"

[[move.package.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.package]]
id = "SuiSystem"

[move.package.source]
git = "https://github.com/MystenLabs/sui.git"
rev = "db951a5ea6b125f6253a6b7f436ddba46eb0feb3"
subdir = "crates/sui-framework/packages/sui-system"

[[move.package.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.package.dependencies]]
id = "Sui"
name = "Sui"

[[move.package]]
id = "Utils"

[move.package.source]
local = "../../utils"

[[move.package.dependencies]]
id = "Bridge"
name = "Bridge"

[[move.package.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.package.dependencies]]
id = "Sui"
name = "Sui"

[[move.package.dependencies]]
id = "SuiSystem"
name = "SuiSystem"

[move.toolchain-version]
compiler-version = "1.54.1"
edition = "2024.beta"
flavor = "sui"

[env]
[env.testnet]
chain-id = "4c78adac"
original-published-id = "0xbd1b4591ff06b36113c28ec2f5ce10151b6a2998699e3d8364ec9f77efe950c8"
latest-published-id = "0xbd1b4591ff06b36113c28ec2f5ce10151b6a2998699e3d8364ec9f77efe950c8"
published-version = "1"

[env.mainnet]
chain-id = "35834a8a"
original-published-id = "0x6b29b0abf9da88be53a9ea1dfab8f600930b9ab6961eee65d67c1eecdd8cb695"
latest-published-id = "0x6b29b0abf9da88be53a9ea1dfab8f600930b9ab6961eee65d67c1eecdd8cb695"
published-version = "1"
