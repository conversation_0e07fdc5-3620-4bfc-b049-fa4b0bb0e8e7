[package]
name = "Executor"
version = "0.0.1"
edition = "2024.beta"
license = "MIT"

[dependencies]
WorkerCommon = { local = "../../worker-common" }
Uln302 = { local = "../../../message-libs/uln-302" }
MsglibPtbBuilderCallTypes = { local = "../../../ptb-builders/msglib-ptb-builders/msglib-ptb-builder-call-types" }
ExecutorCallType = { local = "../executor-call-type" }

[addresses]
executor = "0x0"

[dev-dependencies]

[dev-addresses]