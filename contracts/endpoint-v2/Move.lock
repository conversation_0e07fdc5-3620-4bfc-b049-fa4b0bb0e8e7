[move]
version = 3
manifest_digest = "673821EEB48BC0D6A16A61C7ABF0012CBFFE0EB4C9DFC6E189C624758FFF57E2"
deps_digest = "CAFAD8A7CF51067FB4358215BECB86BD100DD64E57C2AC8A7AE7D74B688F5965"

[[move.dependencies]]
id = "Bridge"
name = "Bridge"

[[move.dependencies]]
id = "Call"
name = "Call"

[[move.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.dependencies]]
id = "Sui"
name = "Sui"

[[move.dependencies]]
id = "SuiSystem"
name = "SuiSystem"

[[move.dependencies]]
id = "Utils"
name = "Utils"

[[move.dependencies]]
id = "Zro"
name = "Zro"

[[move.package]]
id = "Bridge"

[move.package.source]
git = "https://github.com/MystenLabs/sui.git"
rev = "db951a5ea6b125f6253a6b7f436ddba46eb0feb3"
subdir = "crates/sui-framework/packages/bridge"

[[move.package.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.package.dependencies]]
id = "Sui"
name = "Sui"

[[move.package.dependencies]]
id = "SuiSystem"
name = "SuiSystem"

[[move.package]]
id = "Call"

[move.package.source]
local = "../call"

[[move.package.dependencies]]
id = "Bridge"
name = "Bridge"

[[move.package.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.package.dependencies]]
id = "Sui"
name = "Sui"

[[move.package.dependencies]]
id = "SuiSystem"
name = "SuiSystem"

[[move.package.dependencies]]
id = "Utils"
name = "Utils"

[[move.package]]
id = "MoveStdlib"

[move.package.source]
git = "https://github.com/MystenLabs/sui.git"
rev = "db951a5ea6b125f6253a6b7f436ddba46eb0feb3"
subdir = "crates/sui-framework/packages/move-stdlib"

[[move.package]]
id = "Sui"

[move.package.source]
git = "https://github.com/MystenLabs/sui.git"
rev = "db951a5ea6b125f6253a6b7f436ddba46eb0feb3"
subdir = "crates/sui-framework/packages/sui-framework"

[[move.package.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.package]]
id = "SuiSystem"

[move.package.source]
git = "https://github.com/MystenLabs/sui.git"
rev = "db951a5ea6b125f6253a6b7f436ddba46eb0feb3"
subdir = "crates/sui-framework/packages/sui-system"

[[move.package.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.package.dependencies]]
id = "Sui"
name = "Sui"

[[move.package]]
id = "Utils"

[move.package.source]
local = "../utils"

[[move.package.dependencies]]
id = "Bridge"
name = "Bridge"

[[move.package.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.package.dependencies]]
id = "Sui"
name = "Sui"

[[move.package.dependencies]]
id = "SuiSystem"
name = "SuiSystem"

[[move.package]]
id = "Zro"

[move.package.source]
local = "../zro"

[[move.package.dependencies]]
id = "Bridge"
name = "Bridge"

[[move.package.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.package.dependencies]]
id = "Sui"
name = "Sui"

[[move.package.dependencies]]
id = "SuiSystem"
name = "SuiSystem"

[move.toolchain-version]
compiler-version = "1.54.1"
edition = "2024.beta"
flavor = "sui"

[env]
[env.testnet]
chain-id = "4c78adac"
original-published-id = "0x85dd52af20c3d8047685dcb6fddc443934b8cd2b6fbe9d2d262a5f6943f24557"
latest-published-id = "0x85dd52af20c3d8047685dcb6fddc443934b8cd2b6fbe9d2d262a5f6943f24557"
published-version = "1"

[env.mainnet]
chain-id = "35834a8a"
original-published-id = "0xf625a8bde20d64850a4ec7b01c1918d9a0a29495546de7a0144440275f9b933c"
latest-published-id = "0xf625a8bde20d64850a4ec7b01c1918d9a0a29495546de7a0144440275f9b933c"
published-version = "1"
