[move]
version = 3
manifest_digest = "827F6DB0F478DD7EB235BE7BE3BD60899FF53531CE5E1217D9A15C22AFD3F365"
deps_digest = "397E6A9F7A624706DBDFEE056CE88391A15876868FD18A88504DA74EB458D697"

[[move.dependencies]]
id = "Bridge"
name = "Bridge"

[[move.dependencies]]
id = "EndpointV2"
name = "EndpointV2"

[[move.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.dependencies]]
id = "Sui"
name = "Sui"

[[move.dependencies]]
id = "SuiSystem"
name = "SuiSystem"

[[move.package]]
id = "Bridge"

[move.package.source]
git = "https://github.com/MystenLabs/sui.git"
rev = "db951a5ea6b125f6253a6b7f436ddba46eb0feb3"
subdir = "crates/sui-framework/packages/bridge"

[[move.package.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.package.dependencies]]
id = "Sui"
name = "Sui"

[[move.package.dependencies]]
id = "SuiSystem"
name = "SuiSystem"

[[move.package]]
id = "Call"

[move.package.source]
local = "../../call"

[[move.package.dependencies]]
id = "Bridge"
name = "Bridge"

[[move.package.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.package.dependencies]]
id = "Sui"
name = "Sui"

[[move.package.dependencies]]
id = "SuiSystem"
name = "SuiSystem"

[[move.package.dependencies]]
id = "Utils"
name = "Utils"

[[move.package]]
id = "EndpointV2"

[move.package.source]
local = "../../endpoint-v2"

[[move.package.dependencies]]
id = "Bridge"
name = "Bridge"

[[move.package.dependencies]]
id = "Call"
name = "Call"

[[move.package.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.package.dependencies]]
id = "Sui"
name = "Sui"

[[move.package.dependencies]]
id = "SuiSystem"
name = "SuiSystem"

[[move.package.dependencies]]
id = "Utils"
name = "Utils"

[[move.package.dependencies]]
id = "Zro"
name = "Zro"

[[move.package]]
id = "MoveStdlib"

[move.package.source]
git = "https://github.com/MystenLabs/sui.git"
rev = "db951a5ea6b125f6253a6b7f436ddba46eb0feb3"
subdir = "crates/sui-framework/packages/move-stdlib"

[[move.package]]
id = "Sui"

[move.package.source]
git = "https://github.com/MystenLabs/sui.git"
rev = "db951a5ea6b125f6253a6b7f436ddba46eb0feb3"
subdir = "crates/sui-framework/packages/sui-framework"

[[move.package.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.package]]
id = "SuiSystem"

[move.package.source]
git = "https://github.com/MystenLabs/sui.git"
rev = "db951a5ea6b125f6253a6b7f436ddba46eb0feb3"
subdir = "crates/sui-framework/packages/sui-system"

[[move.package.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.package.dependencies]]
id = "Sui"
name = "Sui"

[[move.package]]
id = "Utils"

[move.package.source]
local = "../../utils"

[[move.package.dependencies]]
id = "Bridge"
name = "Bridge"

[[move.package.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.package.dependencies]]
id = "Sui"
name = "Sui"

[[move.package.dependencies]]
id = "SuiSystem"
name = "SuiSystem"

[[move.package]]
id = "Zro"

[move.package.source]
local = "../../zro"

[[move.package.dependencies]]
id = "Bridge"
name = "Bridge"

[[move.package.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.package.dependencies]]
id = "Sui"
name = "Sui"

[[move.package.dependencies]]
id = "SuiSystem"
name = "SuiSystem"

[move.toolchain-version]
compiler-version = "1.54.1"
edition = "2024.beta"
flavor = "sui"

[env]
[env.testnet]
chain-id = "4c78adac"
original-published-id = "0xd4f4a48672c410085ad531efa1d3e19aaa21483da02fe1c8888ef6fa8d8ee330"
latest-published-id = "0xd4f4a48672c410085ad531efa1d3e19aaa21483da02fe1c8888ef6fa8d8ee330"
published-version = "1"

[env.mainnet]
chain-id = "35834a8a"
original-published-id = "0x5ccc92487b6d38cf5e327c8c5178776d48dbf82020f25b2f766dcf3dfd669752"
latest-published-id = "0x5ccc92487b6d38cf5e327c8c5178776d48dbf82020f25b2f766dcf3dfd669752"
published-version = "1"
