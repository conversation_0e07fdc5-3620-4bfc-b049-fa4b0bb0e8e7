[package]
name = "OFT"
version = "0.0.1"
edition = "2024.beta" # edition = "legacy" to use legacy (pre-2024) Move
license = "MIT"       # e.g., "MIT", "GPL", "Apache 2.0"

[dependencies]
OApp = { local = "../../oapp" }
OFTComposerCommon = { local = "../oft-composer-common" }
Call = { local = "../../../call", override = true }
EndpointV2 = { local = "../../../endpoint-v2", override = true }

[addresses]
oft = "0x0"

[dev-dependencies]
SimpleMessageLib = { local = "../../../message-libs/simple-message-lib" }

[dev-addresses]
