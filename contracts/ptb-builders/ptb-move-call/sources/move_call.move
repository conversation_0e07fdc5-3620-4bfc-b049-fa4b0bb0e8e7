/// Module for representing individual Move function calls in Programmable Transaction Blocks.
/// Enables dynamic PTB construction through modular PTB builders, allowing runtime selection
/// of protocol components while overcoming Move's static dispatch limitations.
module ptb_move_call::move_call;

use ptb_move_call::{argument::Argument, function::{Self, Function}};
use std::{ascii::String, type_name::TypeName};
use utils::bytes32::Bytes32;

// === Structs ===

/// Represents a Move function call within a Programmable Transaction Block (PTB).
///
/// Enables LayerZero's modular architecture by supporting dynamic PTB construction:
/// - **Direct calls**: Execute directly on blockchain, require no preprocessing
/// - **Builder calls**: Route to component-specific PTB builders for dynamic expansion
///
/// Builder calls solve the core problem: LayerZero components (message libs, workers, etc.)
/// are selected at runtime based on configuration, but each requires different PTB structures.
/// This enables modular PTB construction where each component provides its own PTB builder.
public struct MoveCall has copy, drop, store {
    // Target function to call (package::module::function)
    function: Function,
    // Arguments to pass to the function call
    arguments: vector<Argument>,
    // Type arguments for generic function parameters
    type_arguments: vector<TypeName>,
    // Whether this call requires component-specific PTB Builder expansion before blockchain submission.
    // - true: Builder call - routes to component PTB builder for dynamic expansion based on runtime config
    // - false: Direct call - goes directly to blockchain without preprocessing
    is_builder_call: bool,
    // Global IDs for move call results, enabling cross-PTB argument mapping during construction.
    // For direct calls: Only assign IDs to results needed by other PTBs generated by builder calls,
    // use 0 for unused results. If all results are not needed, it can be empty.
    // For builder calls: All expected result IDs that the builder expansion must produce without any 0s
    result_ids: vector<Bytes32>,
}

// === Creators ===

/// Creates a new MoveCall with the specified parameters.
public fun create(
    package: address,
    module_name: String,
    function_name: String,
    arguments: vector<Argument>,
    type_arguments: vector<TypeName>,
    is_builder_call: bool,
    result_ids: vector<Bytes32>,
): MoveCall {
    MoveCall {
        function: function::create(package, module_name, function_name),
        arguments,
        type_arguments,
        is_builder_call,
        result_ids,
    }
}

// === View Functions ===

/// Returns the target function to be called.
public fun function(self: &MoveCall): &Function {
    &self.function
}

/// Returns the arguments to pass to the function call.
public fun arguments(self: &MoveCall): &vector<Argument> {
    &self.arguments
}

/// Returns a mutable reference to the arguments.
public fun arguments_mut(self: &mut MoveCall): &mut vector<Argument> {
    &mut self.arguments
}

/// Returns the type arguments for generic function parameters.
public fun type_arguments(self: &MoveCall): &vector<TypeName> {
    &self.type_arguments
}

/// Returns whether this call requires component-specific PTB Builder expansion before blockchain submission.
public fun is_builder_call(self: &MoveCall): bool {
    self.is_builder_call
}

/// Returns the global IDs of the call results.
public fun result_ids(self: &MoveCall): &vector<Bytes32> {
    &self.result_ids
}
