# PTB Move Call Specification

## Abstract

This specification defines a dynamic Programmable Transaction Block (PTB) construction system for LayerZero V2, enabling runtime component selection through modular PTB builders and cross-PTB argument mapping via global identifiers.

## 1. Introduction

### 1.1 Problem Statement

LayerZero V2's modular architecture requires runtime selection of protocol components (message libraries and workers) based on OApp configuration. This creates fundamental challenges:

- **Runtime Component Selection**: Components are chosen dynamically based on configuration
- **Static PTB Structure**: Each component requires different call sequences and arguments
- **Cross-PTB Communication**: Arguments must flow between separately constructed PTB fragments
- **Move Language Limitations**: Static dispatch prevents dynamic contract invocation

### 1.2 Solution Overview

This specification introduces two core mechanisms:

1. **Dynamic PTB Generation**: Component PTBs are dynamically generated by builder modules to expand builder calls from the root PTB
2. **Global ID System**: Pre-assigned identifiers enabling cross-PTB argument mapping during construction

## 2. Terminology

### 2.1 Definitions

- **Direct Call**: A Move function call that executes directly on the blockchain without preprocessing
- **Builder Call**: A component-specific function call that routes to a PTB builder module for expansion into multiple calls
- **Global ID**: A pre-assigned unique identifier for call results, enabling cross-PTB references
- **PTB Construction Scope**: The boundary within which `NestedResult` references are valid
- **Root PTB**: The initial PTB containing direct calls and/or builder calls, serves as the starting point for PTB construction
- **Component PTB Builder**: A builder module that expands component-specific builder calls
- **Component PTB**: A PTB produced by a component builder to expand a builder call

### 2.2 Data Structures

```typescript
interface MoveCall {
  function: string;                   // Target function identifier
  args: Argument[];                   // Function arguments
  is_builder_call: boolean;           // Call type designation
  result_ids: (bytes32)[];            // Global IDs for results
}

enum Argument {
  ID(bytes32),                        // Global ID reference
  Object(ObjectID),                   // Object reference
  Pure(bytes),                        // Pure value
  NestedResult(u16, u16)              // Same-PTB result reference
}
```

## 3. Core Mechanisms

### 3.1 Dynamic PTB Generation System

#### 3.1.1 Purpose

Dynamic PTB generation enables runtime component selection by allowing builder modules to construct appropriate PTB structures based on current state and configuration.

#### 3.1.2 Execution Flow

```
1. Root PTB Construction → Developer creates root PTB with direct calls and/or builder calls
2. Off-chain Simulation → Builder calls route to their respective component builders
3. PTB Expansion → Each builder produces call sequences (may include more builder calls)
4. PTB Assembly → Calls produced by builder combined into the root PTB
5. Recursive Expansion → Process repeats until all builder calls are resolved
6. Blockchain Submission → Final PTB with only direct calls executed atomically
```

#### ******* Example: Nested PTB Generation

The following diagram illustrates how a root PTB with builder calls expands into multiple component PTBs, which may themselves contain builder calls:

```
                        [Root PTB]
                    • Direct Call 1
                    • Builder Call A
                    • Builder Call B
                            │
                            │ expand()
                            ↓
        ┌───────────────────┴───────────────────┐
        │                                       │
        ↓                                       ↓
    [PTB A]                                 [PTB B]
    • Direct Call A1                        • Direct Call B1
    • Builder Call C                        • Direct Call B2
    • Direct Call A2                            │
        │                                       │
        │ expand()                              │
        ↓                                       │
    [PTB C]                                     │
    • Direct Call C1                            │
    • Direct Call C2                            │
        │                                       │
        └───────────────┬───────────────────────┘
                        │
                        │ assemble()
                        ↓
                    [Final PTB]
                    • Direct Call 1
                    • Direct Call A1
                    • Direct Call C1
                    • Direct Call C2
                    • Direct Call A2
                    • Direct Call B1
                    • Direct Call B2
```

In this example:

- **Root PTB** contains one direct call and two builder calls
- **Builder Call A** expands into PTB A, which itself contains another builder call (to PTB C)
- **Builder Call B** expands into PTB B with only direct calls
- **Builder Call C** (from PTB A) expands into PTB C with direct calls
- The final assembled PTB contains only direct calls in the correct execution order

#### 3.1.3 Builder Call Properties

- **Component-Specific Signatures**: Different components have different function signatures and arguments
- **Runtime Selection**: Component selection occurs during PTB generation
- **Result Promises**: Builders must produce all declared global IDs

### 3.2 Global ID System

#### 3.2.1 Purpose

Global IDs create stable references between PTBs during construction, solving the cross-PTB argument passing problem.

#### 3.2.2 Lifecycle

```
1. ID Assignment → result_ids: [global_id] in call definition
2. Cross-PTB Reference → argument.createId(global_id) in other PTBs
3. Builder Fulfillment → Builder PTBs produce promised IDs
4. Final Conversion → SDK converts all IDs to NestedResult indices
```

## 4. Specification Rules

### 4.1 Global ID Assignment Rules

#### 4.1.1 Assignment Requirements (`result_ids` field)

**MUST assign global IDs when:**

- `R1.1`: Result is produced by a builder call and will be consumed within the same PTB construction scope
- `R1.2`: Result is produced by a call (direct or builder) in the parent PTB and will be consumed by child PTBs produced by builder calls

**MUST NOT assign global IDs when:**

- `R1.3`: Result is only consumed within the same PTB construction scope
- `R1.4`: Result is not consumed by any downstream calls

**Implementation:**

- **For assignment**: Use `result_ids: [id1, 0x0, id3]` to assign IDs only to specific results (zero bytes32 for unassigned)
- **For no assignment**: Use `result_ids: []` when no global ID is needed

#### 4.1.2 Reference Requirements (`args` field)

**MUST use `NestedResult(call_index, result_index)` when:**

- `R2.1`: Referencing result from direct call within the same PTB construction scope

**MUST use `Id(global_id)` when:**

- `R2.2`: Referencing result from builder call within the same PTB construction scope
- `R2.3`: Referencing result from parent PTB in child PTB

### 4.2 Formal Constraints

#### 4.2.1 Global ID Uniqueness

- `C1`: Each global ID MUST be unique within the entire PTB construction graph
- `C2`: Global ID assignment MUST occur before any references to that ID

#### 4.2.2 Reference Validity

- `C3`: `NestedResult(i, j)` MUST satisfy `i < current_call_index`
- `C4`: `Id(id)` MUST reference an ID assigned in `result_ids` field
- `C5`: Referenced results MUST exist at the specified indices

#### 4.2.3 Builder Call Constraints

- `C6`: Builder calls MUST produce all globally assigned result IDs declared in `result_ids`
