# @generated by Move, please check-in and do not edit manually.

[move]
version = 3
manifest_digest = "53FC94D7D047F68EC4D17B573336C47F3BB141C79EE6D4CB7BE2E4323F63D1C8"
deps_digest = "8227D341C0550A37580C10FA45FE148A465225149C69D179737E0FE454AD6AEC"
dependencies = [
  { id = "Bridge", name = "Bridge" },
  { id = "Call", name = "Call" },
  { id = "EndpointV2", name = "EndpointV2" },
  { id = "MoveStdlib", name = "MoveStdlib" },
  { id = "OApp", name = "OApp" },
  { id = "OFT", name = "OFT" },
  { id = "PtbMoveCall", name = "PtbMoveCall" },
  { id = "Sui", name = "Sui" },
  { id = "SuiSystem", name = "SuiSystem" },
]

[[move.package]]
id = "Bridge"
source = { git = "https://github.com/MystenLabs/sui.git", rev = "db951a5ea6b125f6253a6b7f436ddba46eb0feb3", subdir = "crates/sui-framework/packages/bridge" }

dependencies = [
  { id = "MoveStdlib", name = "MoveStdlib" },
  { id = "Sui", name = "Sui" },
  { id = "SuiSystem", name = "SuiSystem" },
]

[[move.package]]
id = "Call"
source = { local = "../../../../call" }

dependencies = [
  { id = "Bridge", name = "Bridge" },
  { id = "MoveStdlib", name = "MoveStdlib" },
  { id = "Sui", name = "Sui" },
  { id = "SuiSystem", name = "SuiSystem" },
  { id = "Utils", name = "Utils" },
]

[[move.package]]
id = "EndpointV2"
source = { local = "../../../../endpoint-v2" }

dependencies = [
  { id = "Bridge", name = "Bridge" },
  { id = "Call", name = "Call" },
  { id = "MoveStdlib", name = "MoveStdlib" },
  { id = "Sui", name = "Sui" },
  { id = "SuiSystem", name = "SuiSystem" },
  { id = "Utils", name = "Utils" },
  { id = "Zro", name = "Zro" },
]

[[move.package]]
id = "MessageLibCommon"
source = { local = "../../../../message-libs/message-lib-common" }

dependencies = [
  { id = "Bridge", name = "Bridge" },
  { id = "EndpointV2", name = "EndpointV2" },
  { id = "MoveStdlib", name = "MoveStdlib" },
  { id = "Sui", name = "Sui" },
  { id = "SuiSystem", name = "SuiSystem" },
]

[[move.package]]
id = "MoveStdlib"
source = { git = "https://github.com/MystenLabs/sui.git", rev = "db951a5ea6b125f6253a6b7f436ddba46eb0feb3", subdir = "crates/sui-framework/packages/move-stdlib" }

[[move.package]]
id = "OApp"
source = { local = "../../../oapp" }

dependencies = [
  { id = "Bridge", name = "Bridge" },
  { id = "Call", name = "Call" },
  { id = "EndpointV2", name = "EndpointV2" },
  { id = "MoveStdlib", name = "MoveStdlib" },
  { id = "Sui", name = "Sui" },
  { id = "SuiSystem", name = "SuiSystem" },
]

[[move.package]]
id = "OFT"
source = { local = "../../oft" }

dependencies = [
  { id = "Bridge", name = "Bridge" },
  { id = "Call", name = "Call" },
  { id = "EndpointV2", name = "EndpointV2" },
  { id = "MoveStdlib", name = "MoveStdlib" },
  { id = "OApp", name = "OApp" },
  { id = "OFTComposerCommon", name = "OFTComposerCommon" },
  { id = "Sui", name = "Sui" },
  { id = "SuiSystem", name = "SuiSystem" },
]

dev-dependencies = [
  { id = "SimpleMessageLib", name = "SimpleMessageLib" },
]

[[move.package]]
id = "OFTComposerCommon"
source = { local = "../../oft-composer-common" }

dependencies = [
  { id = "Bridge", name = "Bridge" },
  { id = "Call", name = "Call" },
  { id = "MoveStdlib", name = "MoveStdlib" },
  { id = "Sui", name = "Sui" },
  { id = "SuiSystem", name = "SuiSystem" },
  { id = "Utils", name = "Utils" },
]

[[move.package]]
id = "PtbMoveCall"
source = { local = "../../../../ptb-builders/ptb-move-call" }

dependencies = [
  { id = "Bridge", name = "Bridge" },
  { id = "MoveStdlib", name = "MoveStdlib" },
  { id = "Sui", name = "Sui" },
  { id = "SuiSystem", name = "SuiSystem" },
  { id = "Utils", name = "Utils" },
]

[[move.package]]
id = "SimpleMessageLib"
source = { local = "../../../../message-libs/simple-message-lib" }

dependencies = [
  { id = "Bridge", name = "Bridge" },
  { id = "MessageLibCommon", name = "MessageLibCommon" },
  { id = "MoveStdlib", name = "MoveStdlib" },
  { id = "Sui", name = "Sui" },
  { id = "SuiSystem", name = "SuiSystem" },
  { id = "Utils", name = "Utils" },
]

[[move.package]]
id = "Sui"
source = { git = "https://github.com/MystenLabs/sui.git", rev = "db951a5ea6b125f6253a6b7f436ddba46eb0feb3", subdir = "crates/sui-framework/packages/sui-framework" }

dependencies = [
  { id = "MoveStdlib", name = "MoveStdlib" },
]

[[move.package]]
id = "SuiSystem"
source = { git = "https://github.com/MystenLabs/sui.git", rev = "db951a5ea6b125f6253a6b7f436ddba46eb0feb3", subdir = "crates/sui-framework/packages/sui-system" }

dependencies = [
  { id = "MoveStdlib", name = "MoveStdlib" },
  { id = "Sui", name = "Sui" },
]

[[move.package]]
id = "Utils"
source = { local = "../../../../utils" }

dependencies = [
  { id = "Bridge", name = "Bridge" },
  { id = "MoveStdlib", name = "MoveStdlib" },
  { id = "Sui", name = "Sui" },
  { id = "SuiSystem", name = "SuiSystem" },
]

[[move.package]]
id = "Zro"
source = { local = "../../../../zro" }

dependencies = [
  { id = "Bridge", name = "Bridge" },
  { id = "MoveStdlib", name = "MoveStdlib" },
  { id = "Sui", name = "Sui" },
  { id = "SuiSystem", name = "SuiSystem" },
]

[move.toolchain-version]
compiler-version = "1.54.1"
edition = "2024.beta"
flavor = "sui"
