[move]
version = 3
manifest_digest = "2EE28B05A026ABE74953E7F8048D714F53B0CAC0B86C05A5F8FEBBEFF4753B85"
deps_digest = "CAFAD8A7CF51067FB4358215BECB86BD100DD64E57C2AC8A7AE7D74B688F5965"

[[move.dependencies]]
id = "Bridge"
name = "Bridge"

[[move.dependencies]]
id = "MessageLibCommon"
name = "MessageLibCommon"

[[move.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.dependencies]]
id = "Sui"
name = "Sui"

[[move.dependencies]]
id = "SuiSystem"
name = "SuiSystem"

[[move.dependencies]]
id = "Treasury"
name = "Treasury"

[[move.dependencies]]
id = "Utils"
name = "Utils"

[[move.package]]
id = "Bridge"

[move.package.source]
git = "https://github.com/MystenLabs/sui.git"
rev = "db951a5ea6b125f6253a6b7f436ddba46eb0feb3"
subdir = "crates/sui-framework/packages/bridge"

[[move.package.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.package.dependencies]]
id = "Sui"
name = "Sui"

[[move.package.dependencies]]
id = "SuiSystem"
name = "SuiSystem"

[[move.package]]
id = "Call"

[move.package.source]
local = "../../call"

[[move.package.dependencies]]
id = "Bridge"
name = "Bridge"

[[move.package.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.package.dependencies]]
id = "Sui"
name = "Sui"

[[move.package.dependencies]]
id = "SuiSystem"
name = "SuiSystem"

[[move.package.dependencies]]
id = "Utils"
name = "Utils"

[[move.package]]
id = "EndpointV2"

[move.package.source]
local = "../../endpoint-v2"

[[move.package.dependencies]]
id = "Bridge"
name = "Bridge"

[[move.package.dependencies]]
id = "Call"
name = "Call"

[[move.package.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.package.dependencies]]
id = "Sui"
name = "Sui"

[[move.package.dependencies]]
id = "SuiSystem"
name = "SuiSystem"

[[move.package.dependencies]]
id = "Utils"
name = "Utils"

[[move.package.dependencies]]
id = "Zro"
name = "Zro"

[[move.package]]
id = "MessageLibCommon"

[move.package.source]
local = "../message-lib-common"

[[move.package.dependencies]]
id = "Bridge"
name = "Bridge"

[[move.package.dependencies]]
id = "EndpointV2"
name = "EndpointV2"

[[move.package.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.package.dependencies]]
id = "Sui"
name = "Sui"

[[move.package.dependencies]]
id = "SuiSystem"
name = "SuiSystem"

[[move.package]]
id = "MoveStdlib"

[move.package.source]
git = "https://github.com/MystenLabs/sui.git"
rev = "db951a5ea6b125f6253a6b7f436ddba46eb0feb3"
subdir = "crates/sui-framework/packages/move-stdlib"

[[move.package]]
id = "Sui"

[move.package.source]
git = "https://github.com/MystenLabs/sui.git"
rev = "db951a5ea6b125f6253a6b7f436ddba46eb0feb3"
subdir = "crates/sui-framework/packages/sui-framework"

[[move.package.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.package]]
id = "SuiSystem"

[move.package.source]
git = "https://github.com/MystenLabs/sui.git"
rev = "db951a5ea6b125f6253a6b7f436ddba46eb0feb3"
subdir = "crates/sui-framework/packages/sui-system"

[[move.package.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.package.dependencies]]
id = "Sui"
name = "Sui"

[[move.package]]
id = "Treasury"

[move.package.source]
local = "../treasury"

[[move.package.dependencies]]
id = "Bridge"
name = "Bridge"

[[move.package.dependencies]]
id = "MessageLibCommon"
name = "MessageLibCommon"

[[move.package.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.package.dependencies]]
id = "Sui"
name = "Sui"

[[move.package.dependencies]]
id = "SuiSystem"
name = "SuiSystem"

[[move.package.dependencies]]
id = "Utils"
name = "Utils"

[[move.package]]
id = "Utils"

[move.package.source]
local = "../../utils"

[[move.package.dependencies]]
id = "Bridge"
name = "Bridge"

[[move.package.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.package.dependencies]]
id = "Sui"
name = "Sui"

[[move.package.dependencies]]
id = "SuiSystem"
name = "SuiSystem"

[[move.package]]
id = "Zro"

[move.package.source]
local = "../../zro"

[[move.package.dependencies]]
id = "Bridge"
name = "Bridge"

[[move.package.dependencies]]
id = "MoveStdlib"
name = "MoveStdlib"

[[move.package.dependencies]]
id = "Sui"
name = "Sui"

[[move.package.dependencies]]
id = "SuiSystem"
name = "SuiSystem"

[move.toolchain-version]
compiler-version = "1.54.1"
edition = "2024.beta"
flavor = "sui"

[env]
[env.testnet]
chain-id = "4c78adac"
original-published-id = "0x556bd8fba10ba9e8be88a3ed54b8cbeb8b512b95e5c107cde7d7b10b754069c2"
latest-published-id = "0x556bd8fba10ba9e8be88a3ed54b8cbeb8b512b95e5c107cde7d7b10b754069c2"
published-version = "1"

[env.mainnet]
chain-id = "35834a8a"
original-published-id = "0xd73c9d588717c32fa327335d9beaf59b983b808577a382e013d8c7161a323d8e"
latest-published-id = "0xd73c9d588717c32fa327335d9beaf59b983b808577a382e013d8c7161a323d8e"
published-version = "1"
